<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class CommentsTest extends DuskTestCase
{
    public function testIndex()
    {
        $admin = \App\User::find(1);
        $this->browse(function (Browser $browser) use ($admin) {
            $browser->loginAs($admin);
            $browser->visit(route('admin.comments.index'));
            $browser->assertRouteIs('admin.comments.index');
        });
    }
}
